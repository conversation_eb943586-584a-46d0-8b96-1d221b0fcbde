import type {
  SendCodeResponse,
  VerifyCodeResponse,
  ProfileResponse,
  User,
  AuthClientConfig,
  EmailValidationResult,
  CodeValidationResult
} from './auth-types'

/**
 * 认证客户端工具类
 * 用于管理邮箱验证码登录的所有API调用
 */
export class AuthClient {
  private baseUrl: string
  private timeout: number

  constructor(config: AuthClientConfig = {}) {
    this.baseUrl = config.baseUrl || 'http://localhost:3001'
    this.timeout = config.timeout || 10000
  }

  /**
   * 发送验证码到指定邮箱
   * @param email 邮箱地址
   * @returns 发送结果
   */
  async sendCode(email: string): Promise<SendCodeResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/send-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('发送验证码失败:', error)
      throw error
    }
  }

  /**
   * 使用邮箱和验证码登录
   * @param email 邮箱地址
   * @param code 6位验证码
   * @returns 登录结果，包含token和用户信息
   */
  async verifyCode(email: string, code: string): Promise<VerifyCodeResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/verify-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, code })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      // 如果登录成功，自动保存token和邮箱
      if (result.success && result.token) {
        await this.setToken(result.token)
        await this.setUserEmail(email) // 保存邮箱
      }

      return result
    } catch (error) {
      console.error('验证码登录失败:', error)
      throw error
    }
  }

  /**
   * 获取用户资料（需要认证）
   * @returns 用户资料信息
   */
  async getProfile(): Promise<ProfileResponse> {
    try {
      const token = await this.getToken()

      if (!token) {
        throw new Error('未找到认证token，请先登录')
      }

      const response = await fetch(`${this.baseUrl}/auth/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        // 如果是401错误，清除无效token
        if (response.status === 401) {
          await this.clearToken()
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('获取用户资料失败:', error)
      throw error
    }
  }

  /**
   * 保存认证token到Chrome扩展本地存储
   * @param token 认证token
   */
  async setToken(token: string): Promise<void> {
    await browser.storage.local.set({ authToken: token })
  }

  /**
   * 从Chrome扩展本地存储获取认证token
   * @returns 认证token或null
   */
  async getToken(): Promise<string | null> {
    const result = await browser.storage.local.get(['authToken'])
    return result.authToken || null
  }

  /**
   * 清除保存的认证token
   */
  async clearToken(): Promise<void> {
    await browser.storage.local.remove(['authToken'])
  }

  /**
   * 保存用户邮箱到Chrome扩展本地存储
   * @param email 用户邮箱
   */
  async setUserEmail(email: string): Promise<void> {
    await browser.storage.local.set({ userEmail: email })
  }

  /**
   * 从Chrome扩展本地存储获取用户邮箱
   * @returns 用户邮箱或null
   */
  async getUserEmail(): Promise<string | null> {
    const result = await browser.storage.local.get(['userEmail'])
    return result.userEmail || null
  }

  /**
   * 清除保存的用户邮箱
   */
  async clearUserEmail(): Promise<void> {
    await browser.storage.local.remove(['userEmail'])
  }

  /**
   * 检查用户是否已登录
   * @returns 是否已登录
   */
  async isLoggedIn(): Promise<boolean> {
    const token = await this.getToken()
    return !!token
  }

  /**
   * 登出用户
   */
  async logout(): Promise<void> {
    await this.clearToken()
    await this.clearUserEmail() // 登出时也清除邮箱
    // 可以在这里添加其他登出逻辑，比如清除其他缓存数据
  }

  /**
   * 完整的登录流程示例
   * @param email 邮箱地址
   * @param code 验证码
   * @returns 登录结果
   */
  async login(email: string, code: string) {
    try {
      // 验证登录
      const loginResult = await this.verifyCode(email, code)

      if (!loginResult.success) {
        throw new Error(loginResult.message || '登录失败')
      }

      // 获取用户信息
      const profile = await this.getProfile()

      return {
        success: true,
        user: profile.user,
        token: loginResult.token,
        message: loginResult.message
      }
    } catch (error) {
      console.error('登录流程失败:', error)
      throw error
    }
  }
}

// 创建默认的认证客户端实例
export const authClient = new AuthClient()

// 导出一些便捷的存储工具函数，用于直接操作Chrome扩展存储
export const authStorage = {
  /**
   * 保存认证token到Chrome扩展本地存储
   * @param token 认证token
   */
  async setToken(token: string): Promise<void> {
    await browser.storage.local.set({ authToken: token })
  },

  /**
   * 从Chrome扩展本地存储获取认证token
   * @returns 认证token或null
   */
  async getToken(): Promise<string | null> {
    const result = await browser.storage.local.get(['authToken'])
    return result.authToken || null
  },

  /**
   * 清除保存的认证token
   */
  async clearToken(): Promise<void> {
    await browser.storage.local.remove(['authToken'])
  },

  /**
   * 保存用户邮箱到Chrome扩展本地存储
   * @param email 用户邮箱
   */
  async setUserEmail(email: string): Promise<void> {
    await browser.storage.local.set({ userEmail: email })
  },

  /**
   * 从Chrome扩展本地存储获取用户邮箱
   * @returns 用户邮箱或null
   */
  async getUserEmail(): Promise<string | null> {
    const result = await browser.storage.local.get(['userEmail'])
    return result.userEmail || null
  },

  /**
   * 清除保存的用户邮箱
   */
  async clearUserEmail(): Promise<void> {
    await browser.storage.local.remove(['userEmail'])
  },

  /**
   * 检查用户是否已登录
   * @returns 是否已登录
   */
  async isLoggedIn(): Promise<boolean> {
    const token = await this.getToken()
    return !!token
  },

  /**
   * 获取用户信息（包含邮箱）
   * @returns 用户信息对象
   */
  async getUserInfo(): Promise<{ email: string | null; hasToken: boolean }> {
    const [email, token] = await Promise.all([
      this.getUserEmail(),
      this.getToken()
    ])
    return {
      email,
      hasToken: !!token
    }
  },

  /**
   * 清除所有用户数据
   */
  async clearAllUserData(): Promise<void> {
    await Promise.all([
      this.clearToken(),
      this.clearUserEmail()
    ])
  },

  /**
   * 监听token变化
   * @param callback 回调函数
   */
  onTokenChange(callback: (token: string | null) => void): void {
    browser.storage.local.onChanged.addListener((changes) => {
      if (changes.authToken) {
        callback(changes.authToken.newValue || null)
      }
    })
  },

  /**
   * 监听用户数据变化（token或邮箱）
   * @param callback 回调函数
   */
  onUserDataChange(callback: (data: { token: string | null; email: string | null }) => void): void {
    browser.storage.local.onChanged.addListener((changes) => {
      if (changes.authToken || changes.userEmail) {
        callback({
          token: changes.authToken?.newValue || null,
          email: changes.userEmail?.newValue || null
        })
      }
    })
  }
}

// 导出一些常用的工具函数
export const authUtils = {
  /**
   * 验证邮箱格式
   * @param email 邮箱地址
   * @returns 是否为有效邮箱格式
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * 详细验证邮箱格式
   * @param email 邮箱地址
   * @returns 验证结果详情
   */
  validateEmail(email: string): EmailValidationResult {
    if (!email) {
      return { isValid: false, message: '请输入邮箱地址' }
    }
    if (!this.isValidEmail(email)) {
      return { isValid: false, message: '请输入有效的邮箱地址' }
    }
    return { isValid: true }
  },

  /**
   * 验证验证码格式
   * @param code 验证码
   * @returns 是否为有效的6位数字验证码
   */
  isValidCode(code: string): boolean {
    return /^\d{6}$/.test(code)
  },

  /**
   * 详细验证验证码格式
   * @param code 验证码
   * @returns 验证结果详情
   */
  validateCode(code: string): CodeValidationResult {
    if (!code) {
      return { isValid: false, message: '请输入验证码' }
    }
    if (!this.isValidCode(code)) {
      return { isValid: false, message: '验证码必须是6位数字' }
    }
    return { isValid: true }
  },

  /**
   * 格式化错误信息
   * @param error 错误对象
   * @returns 用户友好的错误信息
   */
  formatError(error: any): string {
    if (typeof error === 'string') {
      return error
    }

    if (error?.message) {
      return error.message
    }

    if (error?.name === 'TypeError' && error?.message?.includes('fetch')) {
      return '网络连接失败，请检查网络后重试'
    }

    return '操作失败，请重试'
  }
}

/*
使用示例：

1. 在content script中使用：
```typescript
import { authClient, authStorage } from '@/entrypoints/utils/auth-client'

// 登录流程
const login = async (email: string, code: string) => {
  try {
    const result = await authClient.login(email, code)
    if (result.success) {
      console.log('登录成功，token已自动保存到Chrome扩展存储')
    }
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 检查登录状态
const checkAuth = async () => {
  const isLoggedIn = await authClient.isLoggedIn()
  if (isLoggedIn) {
    const token = await authClient.getToken()
    console.log('已登录，token:', token)
  }
}

// 使用便捷的存储工具
const useStorage = async () => {
  await authStorage.setToken('your-token')
  const token = await authStorage.getToken()
  const isLoggedIn = await authStorage.isLoggedIn()
  
  // 监听token变化
  authStorage.onTokenChange((newToken) => {
    console.log('Token已更新:', newToken)
  })
}
```

2. 在background script中使用：
```typescript
import { authStorage } from '@/entrypoints/utils/auth-client'

browser.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
  if (message.action === 'getAuthToken') {
    const token = await authStorage.getToken()
    sendResponse({ token })
  }
})
```

3. 在popup或其他扩展页面中通过消息传递：
```typescript
// 获取token
const getToken = async () => {
  const response = await browser.runtime.sendMessage({ action: 'getAuthToken' })
  return response.token
}

// 设置token
const setToken = async (token: string) => {
  await browser.runtime.sendMessage({ action: 'setAuthToken', token })
}
```
*/ 
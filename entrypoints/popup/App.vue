<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { authStorage } from '../utils/auth-client';
// 移除直接导入的API调用
// import { recordUserAction } from '../utils/download-stats';

// 版本号响应式变量
const manifest = ref<any>({});

// 登录状态相关
const isLoggedIn = ref<boolean>(false);
const isCheckingAuth = ref<boolean>(true);
const userInfo = ref<any>(null);

// 检查登录状态
const checkAuthStatus = async (): Promise<boolean> => {
  try {
    const loggedIn = await authStorage.isLoggedIn();
    isLoggedIn.value = loggedIn;

    // 如果已登录，获取用户信息（包括邮箱）
    if (loggedIn) {
      try {
        const userInfoData = await authStorage.getUserInfo();
        userInfo.value = userInfoData;
      } catch (error) {
        console.warn('获取用户信息失败:', error);
        userInfo.value = { email: null, hasToken: loggedIn };
      }
    } else {
      userInfo.value = null;
    }

    return loggedIn;
  } catch (error) {
    console.error('检查登录状态失败:', error);
    isLoggedIn.value = false;
    return false;
  } finally {
    isCheckingAuth.value = false;
  }
};

// 需要登录的操作列表 - 所有功能都需要登录
const requiresLogin = ['exportPdf', 'exportCollect', 'exportWord', 'exportMd', 'exportHtml', 'exportImg', 'copyToFeishu', 'breakCopy', 'exportPdfAll','preview2'];

// 发送消息到后台脚本
const sendMessageToBg = async (action: string): Promise<void> => {
  try {
    // 判断是否登录，如果未登录且操作需要登录，则跳转到登录页面
    if (requiresLogin.includes(action) && !isLoggedIn.value) {
      // 引导用户登录
      await browser.runtime.sendMessage({ action: 'login' });
      window.close();
      return;
    }

    // 发送消息到background script，包含统计信息
    await browser.runtime.sendMessage({
      action,
      needStats: isLoggedIn.value && requiresLogin.includes(action) // 告诉bg是否需要记录统计
    });
    // window.close();

  } catch (error) {
    console.error('操作失败:', error);
  }
};

// 导出处理函数
const handleExportPdf = () => sendMessageToBg("exportCollect");
const handleExportWord = () => sendMessageToBg("exportWord");
const handleExportMd = () => sendMessageToBg("exportMd");
const handleCopyToFeishu = () => sendMessageToBg("copyToFeishu");
const handleExportHtml = () => sendMessageToBg("exportHtml");
const handleExportImg = () => sendMessageToBg("exportImg");
const handleExportCollect = () => sendMessageToBg("exportCollect");
const handleBreakCopy = () => sendMessageToBg("preview2");
const handleExportPdfAll = () => sendMessageToBg("exportPdfAll");
const handleExportPdf2 = () => sendMessageToBg("exportPdf");

// 登录处理
const handleLogin = () => {
  sendMessageToBg("login");
};

// 登出处理
const handleLogout = async () => {
  try {
    await authStorage.clearAllUserData();
    await checkAuthStatus(); // 重新检查登录状态
  } catch (error) {
    console.error('登出失败:', error);
  }
};

// 获取扩展版本号和检查登录状态
onMounted(async () => {
  try {
    manifest.value = browser.runtime.getManifest();
  } catch (error) {
    console.error('获取版本号失败:', error);
  }

  // 检查登录状态
  await checkAuthStatus();

  // 监听用户数据变化
  authStorage.onUserDataChange(async (data) => {
    await checkAuthStatus();
  });
});
</script>

<template>
  <div class="w-64 bg-white border border-gray-200 shadow-lg">
    <!-- Header -->
    <header class="px-4 py-3 bg-gray-50 border-b border-gray-200">
      <!-- 登录状态显示 -->
      <div v-if="isCheckingAuth" class="flex items-center justify-center">
        <div class="text-sm text-gray-500">检查登录状态...</div>
      </div>

      <div v-else-if="isLoggedIn" class="flex items-center justify-between">
        <div class="flex flex-col space-y-1">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span class="text-sm text-gray-700">已登录</span>
          </div>
          <div v-if="userInfo?.email" class="text-xs text-gray-500 truncate max-w-[140px]">
            {{ userInfo.email }}
          </div>
        </div>
        <button class="text-xs text-gray-500 hover:text-gray-700 cursor-pointer" @click="handleLogout">
          登出
        </button>
      </div>

      <div v-else>
        <button
          class="w-full text-sm bg-blue-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-blue-600 transition-colors"
          @click="handleLogin">
          登录
        </button>
      </div>
    </header>

    <!-- Content -->
    <div class="p-4">
      <div class="grid grid-cols-2 gap-3">
        <button @click="handleBreakCopy" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/pdf.svg" alt="PDF" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
         破解复制
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 6 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>
        <!-- PDF Button -->
        <button @click="handleExportPdf" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/pdf.svg" alt="PDF" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            PDF
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 6 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <button @click="handleExportMd" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/md.svg" alt="Markdown" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            Markdown
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <!-- Word Button -->
        <button @click="handleExportWord" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/word.svg" alt="Word" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            Word
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <button @click="handleExportHtml" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/html.svg" alt="HTML" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            HTML
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <button @click="handleCopyToFeishu" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/feishu.svg" alt="Feishu" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            转存（测试版）
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <button @click="handleExportImg" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/img.svg" alt="IMG" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            图片
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <!-- <button @click="handleExportCollect"
          class="cursor-pointer flex flex-col items-center justify-center p-3 text-center border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 group h-20">
          <img src="@/assets/collect.svg" alt="collect" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900">收集</div>
        </button>
        <button @click="handleBreakCopy"
          class="cursor-pointer flex flex-col items-center justify-center p-3 text-center border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 group h-20">
          <img src="@/assets/collect.svg" alt="collect" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900">破解复制</div>
        </button> -->
        <!-- <button @click="handleExportPdfAll"
          class="cursor-pointer flex flex-col items-center justify-center p-3 text-center border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 group h-20">
          <img src="@/assets/collect.svg" alt="collect" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900">批量导出</div>
        </button> -->
        <!-- <button @click="handleExportPdf2"
          class="cursor-pointer flex flex-col items-center justify-center p-3 text-center border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 group h-20">
          <img src="@/assets/collect.svg" alt="collect" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900">导出PDF2</div>
        </button> -->
      </div>

      <!-- 登录提示 -->
      <div v-if="!isLoggedIn && !isCheckingAuth" class="mt-3 p-2 bg-orange-50 border border-orange-200 rounded-lg">
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd" />
          </svg>
          <span class="text-xs text-orange-700">所有功能需要登录后使用</span>
        </div>
      </div>

      <div class="text-sm text-gray-600">
        qq群： 741683982 <span class="inline-block mt-1 px-2 py-0.5 font-medium text-gray-500 rounded">
          v {{ manifest?.version || '-' }}
        </span>
      </div>
    </div>
  </div>
</template>